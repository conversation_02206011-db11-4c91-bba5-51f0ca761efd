"use client";

import type { CodeforcesSubmission } from "@/lib/codeforces";
import {
  generateProblemId,
  isProblemCompleted,
  markProblemCompleted,
  markProblemIncomplete,
} from "@/lib/codingSheet";
import React from "react";

// ============================================================================
// RATING-BASED PROBLEM ORGANIZER COMPONENT
// ============================================================================
// Groups problems by their difficulty rating with collapsible sections
// Provides completion tracking and rating-based organization

interface RatingBasedProblemOrganizerProps {
  problems: CodeforcesSubmission[]; // Array of problems to organize
  onProblemToggle?: (problemId: string, completed: boolean) => void; // Callback when problem completion changes
}

// Interface for grouped problems by rating
interface RatingGroup {
  rating: number;
  problems: CodeforcesSubmission[];
  completed: number;
  total: number;
}

// Helper function to get rating color based on Codeforces rating system
const getRatingColor = (rating?: number): string => {
  if (!rating) return "#808080"; // Unrated (gray)
  if (rating < 1200) return "#808080"; // Newbie (gray)
  if (rating < 1400) return "#008000"; // Pupil (green)
  if (rating < 1600) return "#03A89E"; // Specialist (cyan)
  if (rating < 1900) return "#0000FF"; // Expert (blue)
  if (rating < 2100) return "#AA00AA"; // Candidate Master (violet)
  if (rating < 2300) return "#FF8C00"; // Master (orange)
  if (rating < 2400) return "#FF8C00"; // International Master (orange)
  if (rating < 2600) return "#FF0000"; // Grandmaster (red)
  if (rating < 3000) return "#FF0000"; // International Grandmaster (red)
  return "#AA0000"; // Legendary Grandmaster (dark red)
};

export const RatingBasedProblemOrganizer: React.FC<
  RatingBasedProblemOrganizerProps
> = ({ problems, onProblemToggle }) => {
  // State for collapsed sections - initialize all sections as collapsed
  const [collapsedSections, setCollapsedSections] = React.useState<Set<number>>(
    new Set()
  );

  // State for problem completion status (local state for immediate UI updates)
  const [localCompletionStatus, setLocalCompletionStatus] = React.useState<
    Record<string, boolean>
  >({});

  // Initialize local completion status from localStorage
  React.useEffect(() => {
    const initialStatus: Record<string, boolean> = {};
    problems.forEach((problem) => {
      const problemId = generateProblemId(problem);
      initialStatus[problemId] = isProblemCompleted(problemId);
    });
    setLocalCompletionStatus(initialStatus);
  }, [problems]);

  // Group problems by rating
  const ratingGroups = React.useMemo(() => {
    const groups: Record<number, CodeforcesSubmission[]> = {};

    // Group problems by rating
    problems.forEach((problem) => {
      const rating = problem.problem.rating || 0; // Use 0 for unrated problems
      if (!groups[rating]) {
        groups[rating] = [];
      }
      groups[rating].push(problem);
    });

    // Convert to array and sort by rating
    const sortedGroups: RatingGroup[] = Object.entries(groups)
      .map(([rating, problemList]) => {
        const ratingNum = parseInt(rating);
        const completed = problemList.filter((p) => {
          const problemId = generateProblemId(p);
          return localCompletionStatus[problemId];
        }).length;

        return {
          rating: ratingNum,
          problems: problemList.sort((a, b) =>
            a.problem.name.localeCompare(b.problem.name)
          ),
          completed,
          total: problemList.length,
        };
      })
      .sort((a, b) => {
        // Sort unrated (0) problems last, then by rating ascending
        if (a.rating === 0 && b.rating !== 0) return 1;
        if (a.rating !== 0 && b.rating === 0) return -1;
        return a.rating - b.rating;
      });

    return sortedGroups;
  }, [problems, localCompletionStatus]);

  // Initialize all sections as collapsed when rating groups change
  React.useEffect(() => {
    if (ratingGroups.length > 0) {
      const allRatings = new Set(ratingGroups.map((group) => group.rating));
      setCollapsedSections(allRatings);
    }
  }, [ratingGroups.length]); // Only depend on the length to avoid infinite loops

  // Toggle section collapse
  const toggleSection = (rating: number) => {
    const newCollapsed = new Set(collapsedSections);
    if (newCollapsed.has(rating)) {
      newCollapsed.delete(rating);
    } else {
      newCollapsed.add(rating);
    }
    setCollapsedSections(newCollapsed);
  };

  // Handle problem completion toggle
  const handleProblemToggle = (problem: CodeforcesSubmission) => {
    const problemId = generateProblemId(problem);
    const currentStatus = localCompletionStatus[problemId] || false;
    const newStatus = !currentStatus;

    // Update local state immediately for responsive UI
    setLocalCompletionStatus((prev) => ({
      ...prev,
      [problemId]: newStatus,
    }));

    // Update localStorage
    if (newStatus) {
      markProblemCompleted(problemId);
    } else {
      markProblemIncomplete(problemId);
    }

    // Call parent callback if provided
    onProblemToggle?.(problemId, newStatus);
  };

  // Generate problem URL
  const getProblemUrl = (problem: CodeforcesSubmission): string => {
    const { problem: p } = problem;
    if (p.contestId) {
      return `https://codeforces.com/contest/${p.contestId}/problem/${p.index}`;
    } else if (p.problemsetName) {
      return `https://codeforces.com/problemset/problem/${p.problemsetName}/${p.index}`;
    }
    return "https://codeforces.com/problemset";
  };

  if (problems.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-400">No problems to organize.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-200">Problems by Rating</h2>
        <div className="text-sm text-gray-400">
          {ratingGroups.length} rating levels • {problems.length} total problems
        </div>
      </div>

      {ratingGroups.map((group) => {
        const isCollapsed = collapsedSections.has(group.rating);
        const ratingColor = getRatingColor(group.rating);
        const completionPercentage =
          group.total > 0
            ? Math.round((group.completed / group.total) * 100)
            : 0;

        return (
          <div
            key={group.rating}
            className="bg-gray-800 border border-gray-700 rounded-lg overflow-hidden"
          >
            {/* Section Header */}
            <button
              onClick={() => toggleSection(group.rating)}
              className="w-full px-6 py-4 flex items-center justify-between hover:bg-gray-750 transition-all duration-200 cursor-pointer hover:scale-[1.01] active:scale-[0.99]"
            >
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <div
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: ratingColor }}
                  ></div>
                  <span className="text-lg font-semibold text-gray-200">
                    {group.rating === 0 ? "Unrated" : group.rating}
                  </span>
                </div>
                <div className="flex items-center gap-4 text-sm text-gray-400">
                  <span>
                    {group.completed} / {group.total} completed
                  </span>
                  <span className="px-2 py-1 bg-gray-700 rounded text-xs">
                    {completionPercentage}%
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-24 bg-gray-600 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300 shadow-lg shadow-blue-500/30"
                    style={{ width: `${completionPercentage}%` }}
                  ></div>
                </div>
                <svg
                  className={`w-5 h-5 text-gray-400 transition-transform ${
                    isCollapsed ? "rotate-0" : "rotate-90"
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </div>
            </button>

            {/* Section Content */}
            {!isCollapsed && (
              <div className="px-6 pb-4 space-y-3">
                {group.problems.map((problem, index) => {
                  const problemId = generateProblemId(problem);
                  const isCompleted = localCompletionStatus[problemId] || false;
                  const problemUrl = getProblemUrl(problem);

                  return (
                    <div
                      key={`${problemId}-${index}`}
                      className={`p-4 rounded-lg border transition-all ${
                        isCompleted
                          ? "bg-green-900/20 border-green-700/50"
                          : "bg-gray-700/50 border-gray-600"
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3 flex-1">
                          <input
                            type="checkbox"
                            checked={isCompleted}
                            onChange={() => handleProblemToggle(problem)}
                            className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 cursor-pointer"
                          />
                          <div className="flex-1">
                            <a
                              href={problemUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className={`font-medium transition-colors cursor-pointer ${
                                isCompleted
                                  ? "text-green-400 hover:text-green-300 line-through"
                                  : "text-blue-500 hover:text-blue-400"
                              }`}
                            >
                              {problem.problem.contestId
                                ? `${problem.problem.contestId}${problem.problem.index}. ${problem.problem.name}`
                                : `${problem.problem.index}. ${problem.problem.name}`}
                            </a>
                            <div className="flex items-center gap-2 mt-1">
                              {problem.problem.tags
                                .slice(0, 3)
                                .map((tag, tagIndex) => (
                                  <span
                                    key={tagIndex}
                                    className="px-2 py-1 bg-gray-600 text-gray-300 text-xs rounded-full"
                                  >
                                    {tag}
                                  </span>
                                ))}
                              {problem.problem.tags.length > 3 && (
                                <span className="text-xs text-gray-500">
                                  +{problem.problem.tags.length - 3} more
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                        <a
                          href={problemUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-all duration-200 cursor-pointer hover:scale-105 active:scale-95"
                        >
                          Solve
                        </a>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};
