"use client";

import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

const HeroSection = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen text-center px-4 md:px-6 animate-in fade-in duration-1000">
      {/* Main Heading with Codeforces-inspired Gradient */}
      <h1 className="text-5xl sm:text-6xl md:text-7xl font-bold tracking-wide mb-4 leading-tight">
        <span className="bg-gradient-to-r from-yellow-400 via-blue-500 to-red-500 bg-clip-text text-transparent">
          MyCPTrainer
        </span>
      </h1>

      {/* Subheading */}
      <h2 className="text-lg sm:text-xl md:text-2xl font-light tracking-wider text-slate-300 mb-12 opacity-90">
        Precision Over Randomness
      </h2>

      {/* Call to Action */}
      <div className="group">
        <Link href="/sheetscope">
          <Button
            variant="outline"
            className="py-3 px-8 rounded-full border-2 border-blue-400/50 text-white hover:bg-blue-500/20 hover:border-blue-300 hover:scale-105 transition-all duration-300 font-medium backdrop-blur-sm"
          >
            Get Started
            <span className="ml-2 group-hover:translate-x-1 transition-transform duration-300">
              →
            </span>
          </Button>
        </Link>
      </div>

      {/* Bottom Decorative Element */}
      <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2">
        <div className="flex space-x-2 opacity-40">
          <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-150"></div>
          <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse delay-300"></div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
