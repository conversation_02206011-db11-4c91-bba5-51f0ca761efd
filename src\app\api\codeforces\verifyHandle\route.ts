import prisma from "@/lib/prisma";
import { createClient } from "@/lib/supabase/server";
import { type NextRequest } from "next/server";

// Handle verification API:
// 1. Handle must not exist in database OR exist without email (can claim)
// 2. Contest participation validation handled on frontend
export async function POST(request: NextRequest) {
  const supabase = await createClient();

  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return new Response("Unauthorized", { status: 401 });
  }

  const email = user.email;

  if (!email) {
    return new Response("Bad Request - no email found", { status: 400 });
  }

  const body = await request.json();
  const handle: string = body.handle;
  const rating: number = body.rating;
  const rank: string = body.rank;

  if (!handle) {
    return new Response("Bad Request - handle required", { status: 400 });
  }

  const lowercaseHandle = handle.toLowerCase();

  try {
    // VALIDATION 1: Check if handle already exists in database
    const existingHandleRecord = await prisma.users.findFirst({
      where: {
        handle: lowercaseHandle,
      },
    });

    if (existingHandleRecord) {
      // Check if existing handle has an email
      if (existingHandleRecord.email) {
        // Handle exists with email - block verification completely
        return new Response(
          JSON.stringify({
            success: false,
            error: "Handle already exists",
            message:
              "This Codeforces handle is already connected to an account. Each handle can only be verified once.",
            existingEmail: existingHandleRecord.email,
          }),
          {
            status: 409,
            headers: { "Content-Type": "application/json" },
          }
        );
      }
      // If no email, we'll update this record later (after contest validation)
    }

    // Contest participation validation is handled on the frontend

    // Database validation passed - create or update user record
    let user;
    let action;

    if (existingHandleRecord && !existingHandleRecord.email) {
      // Update existing record that has no email
      user = await prisma.users.update({
        where: {
          id: existingHandleRecord.id,
        },
        data: {
          email: email,
          rating: rating,
          rank: rank,
          chat_id: "NA",
        },
      });
      action = "updated";
    } else {
      // Create new user record
      user = await prisma.users.create({
        data: {
          handle: lowercaseHandle,
          email: email,
          chat_id: "NA",
          rating: rating,
          rank: rank,
        },
      });
      action = "created";
    }

    return new Response(
      JSON.stringify({
        success: true,
        action: action,
        message: "Codeforces handle verified successfully",
        user: user,
      }),
      {
        status: 201,
        headers: { "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Database error: Failed the handle verification", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

// Checking if the user's email is there in the table : If yes then that means that the codeforces profile is already verified, else it is not verified.
export async function GET(request: NextRequest) {
  const supabase = await createClient();

  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return new Response("Unauthorized", { status: 401 });
  }

  const email = user.email;

  if (!email) {
    return new Response("Bad Request", { status: 400 });
  }

  try {
    const dbUser = await prisma.users.findFirst({
      where: {
        email: email,
      },
      select: {
        handle: true,
      },
    });

    if (!dbUser) {
      return new Response(JSON.stringify({ message: "User not found" }), {
        status: 404,
        headers: { "Content-Type": "application/json" },
      });
    }
    return new Response(JSON.stringify(dbUser), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error("Database error: Failed to fetch user", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}
